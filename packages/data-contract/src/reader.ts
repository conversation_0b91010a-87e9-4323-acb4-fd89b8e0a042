import * as fs from 'fs';
import * as yaml from 'js-yaml';
import * as path from 'path';

// Define the expected structure for a column
export interface Column {
    businessName: string;
    description: string;
    dataType: string;
    businessRules?: string[];
}

// Define the expected structure for a table
export interface Table {
    businessName: string;
    description: string;
    columns: { [key: string]: Column };
}

// Define the expected structure for a tool
export interface Tool {
    description: string;
    readOnlyHint: boolean;
    input_schema: object;
    response_schema: object;
}

// Define the expected structure for the data contract
export interface DataContract {
    tables: { [key: string]: Table };
    abbreviations?: { [key: string]: string };
    tools?: { [key: string]: Tool };
}

export function readDataContract(contractPath: string): DataContract {
    if (!fs.existsSync(contractPath)) {
        throw new Error(`Data contract file not found at: ${contractPath}`);
    }

    const fileContents = fs.readFileSync(contractPath, 'utf8');
    return yaml.load(fileContents) as DataContract;
}

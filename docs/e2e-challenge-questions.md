# End-to-End Challenge Questions

This document contains the official set of plain-English questions for the Layer 2 AI Benchmark Test as defined in Story 2.4. These questions are designed to test the specific business rules and semantic mappings captured in the `datacontract.yml`.

### **Challenge Questions & Expected Outcomes**

1.  **Question:** "Show me the full name for the customer with ID 123."
    * **Purpose:** Tests a simple mapping from `CUST_MST.c_name` to "Customer Name".
    * **Expected SQL:** `SELECT c_name FROM CUST_MST WHERE c_id = 123;`

2.  **Question:** "Which sales orders are cancelled?"
    * **Purpose:** Tests the business rule mapping for `so_hdr.ord_stat` where 'Cancelled' corresponds to the integer code `4`.
    * **Expected SQL:** `SELECT ord_id FROM so_hdr WHERE ord_stat = 4;`

3.  **Question:** "List all products in the 'Product Catalog'."
    * **Purpose:** Tests the mapping of the `product_catalog` table.
    * **Expected SQL:** `SELECT p_name, price FROM product_catalog;`

4.  **Question:** "What was the quantity of each product sold in order number 987?"
    * **Purpose:** Tests the ability to join `sales_transactions` and `product_catalog` and understand the relationship between an order and its transaction lines.
    * **Expected SQL:** `SELECT T2.p_name, T1.quantity FROM sales_transactions AS T1 JOIN product_catalog AS T2 ON T1.p_id = T2.p_id WHERE T1.ord_id = 987;`

5.  **Question:** "Find the name of the customer who placed order number 987."
    * **Purpose:** Tests a three-table join, linking a customer name to a specific order.
    * **Expected SQL:** (Conceptual) `SELECT c.c_name FROM CUST_MST c JOIN so_hdr s ON c.c_id = s.c_id WHERE s.ord_id = 987;`
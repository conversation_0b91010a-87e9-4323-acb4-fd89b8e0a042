# AI Benchmark Test Report

This report documents the results of the Layer 2 AI Benchmark Test as defined in Story 2.4. The test was conducted by manually asking the "challenge questions" to the configured Reference AI Host (<PERSON> v2.5) and recording its final, human-readable answer.

**Date of Test:** YYYY-MM-DD
**Tester:** [Your Name]

---

### **Benchmark Results**

| Question ID | Challenge Question                                                      | AI's Answer | Correct Answer (from DB) | Result (Correct/Incorrect) |
| :---------- | :---------------------------------------------------------------------- | :---------- | :----------------------- | :------------------------- |
| 1           | "Show me the full name for the customer with ID 123."                   |             | `[Expected Name]`        |                            |
| 2           | "Which sales orders are cancelled?"                                     |             | `[Expected Order IDs]`   |                            |
| 3           | "List all products in the 'Product Catalog'."                           |             | `[List of Products]`     |                            |
| 4           | "What was the quantity of each product sold in order number 987?"       |             | `[List of Products/Qtys]`|                            |
| 5           | "Find the name of the customer who placed order number 987."            |             | `[Expected Name]`        |                            |

---

### **Final Accuracy Score**

*   **Total Questions:** 5
*   **Correct Answers:** [Number of Correct Answers]
*   **Accuracy:** [ (Correct Answers / Total Questions) * 100 ] %

**Conclusion:**
The Query Accuracy Rate KPI of >95% was **[Met/Not Met]**.

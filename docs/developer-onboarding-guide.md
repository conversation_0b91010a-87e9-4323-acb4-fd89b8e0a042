# Developer Onboarding Guide: Project Concord

Welcome to Project Concord. This guide provides all the necessary information to set up your local development environment, understand our key processes, and start contributing.

### **1. Local Development Setup**

This section outlines the prerequisites and steps to get the Project Concord monorepo running on your local machine.

**Prerequisites:**
* Node.js (~20.x LTS)
* npm (comes with Node.js)
* Docker and Docker Compose
* Git

**Setup Steps:**
1.  Clone the repository from GitHub.
2.  Navigate to the root directory of the project.
3.  Install all dependencies using the command: `npm install`
4.  Configure your local environment by creating a `.env` file in the project root. Copy the contents of `.env.example` and populate it with the necessary credentials and configuration details.

### **2. Running the Test Environment**

Our testing strategy relies on a containerized, isolated clone of the legacy database. This ensures all testing is safe, reliable, and does not impact production systems. The database service, `db`, is a PostgreSQL instance, as defined in the `docker-compose.yml` file.

1.  **Start the environment:** To start the test database (PostgreSQL) and Redis cache, run the following command from the project root:
    ```bash
    docker-compose up
    ```
2.  **Seed the database:** To populate the test database with sanitized, representative data, run:
    ```bash
    npm run seed-db
    ```

### **3. Secure Access Protocol**

This protocol defines the requirements for securely connecting to the legacy database.

* **Credentials:** All database credentials must be loaded from environment variables and never hardcoded into the source code. Please see the `.env.example` file for the required variables.
* **Network Access:** The PostgreSQL database is not exposed to the public internet. It is only accessible from within the Docker network to the `mcp-server` service, ensuring a secure connection.

### **4. Toolchain Specification**

This specification defines the approved libraries and commands for core project tasks.

*   **Linting:** Code quality is maintained using ESLint. Run `npm run lint` to check for issues.
*   **Testing:** Unit and integration tests are run using Jest. Run `npm run test` to execute the test suite.
*   **Schema Audit:** The schema audit script (`npm run audit-schema`) should be built using the libraries and patterns established in the `mcp-server` package.
*   **Type Checking:** TypeScript is used for static type checking. The `tsconfig.json` files in each package define the compiler options.

### **5. Data Contract Governance Workflow**

The `datacontract.yml` is the single source of truth for our schema's semantic definitions. Any changes to this file must follow this governance process:

1.  A developer creates a pull request with the proposed changes.
2.  The `npm run validate-contract` CI step must pass.
3.  The changes must be formally reviewed and approved by the stakeholders defined in the RACI matrix below.

**RACI Matrix:**
* **Responsible:** Data Steward
* **Accountable:** Head of Data Governance
* **Consulted:** Business Domain Experts, Lead Developer
* **Informed:** All Development Team members

### **6. E2E Test Target**

* **Reference AI Host:** For consistency in end-to-end (E2E) testing, the official target is **Claude Desktop v2.5**.
* **Configuration:** To connect Claude Desktop to your local MCP server, follow these steps:
    1.  **Start the local server:** From the project root, run `npm run dev -w mcp-server`. This will start the server, typically on `http://localhost:8787`.
    2.  **Open Claude Desktop:** Launch the Claude Desktop application.
    3.  **Navigate to Tools:** Go to the "Settings" or "Preferences" menu and find the "Tools" or "Custom Tools" section.
    4.  **Add a New Tool:**
        *   **Tool Name:** `Project Concord Local`
        *   **Tool URL:** `http://localhost:8787/mcp/tools` (or the appropriate URL if your server is on a different port).
        *   **Authentication:** Set the authentication type to "API Key" and provide the secret key defined in your local `.env` file.
    5.  **Save and Enable:** Save the new tool configuration and ensure it is enabled for use in your conversations.

### **7. CI/CD Pipeline**

A foundational CI/CD pipeline is configured using GitHub Actions to ensure code quality and prevent regressions. The workflow runs automatically on all pushes and pull requests to the `main` branch. For details, please see the workflow file at `.github/workflows/ci.yml`.
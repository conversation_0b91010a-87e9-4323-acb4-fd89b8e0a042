### **Story 2.1: Basic MCP Server Implementation**

**Status:** Draft

**Story**
* **As a** Developer,
* **I want** to implement a basic, headless MCP server that starts, correctly handles the protocol handshake, and provides comprehensive observability,
* **so that** we have a functioning and testable server skeleton to which we can add our semantic mediation tools.

**Acceptance Criteria**
1.  The server starts via a script (e.g., `npm start`) within its Docker container.
2.  The server correctly handles the MCP lifecycle management handshake with a connecting client.
3.  All incoming requests and outgoing responses are logged in a structured format to the standard output, fulfilling the core of `NFR3 (Comprehensive Observability)`.
4.  The "Developer Onboarding Guide" is updated to define a "Reference AI Host" (e.g., "Claude Desktop v2.5") to be used for all E2E testing.

**Tasks / Subtasks**
* [ ] **Task 1: Implement the Core Server (AC: 1, 2)**
    * [ ] In the `apps/mcp-server` package, create the main `index.ts` entry point.
    * [ ] Use the `@modelcontextprotocol/sdk` to initialize a new MCP server instance.
    * [ ] Implement the basic server logic to handle startup and the MCP handshake.
* [ ] **Task 2: Implement Observability (AC: 3)**
    * [ ] Add a logging middleware or wrapper around the server's request handler.
    * [ ] Ensure every incoming JSON-RPC request and its corresponding response is logged to the console in a structured JSON format.
    * [ ] Each log entry must include a timestamp and a request ID for correlation.
* [ ] **Task 3: Update Onboarding Guide (AC: 4)**
    * [ ] Add a new section to `docs/developer-onboarding-guide.md` titled "E2E Test Target".
    * [ ] Document that the specified version of the "Reference AI Host" is the official target for all E2E testing.
* [ ] **Task 4: Add Unit Tests**
    * [ ] Write basic unit tests to verify that the server instance can be created and that the logging middleware functions as expected.

**Dev Notes**
* **Foundation**: This story creates the fundamental skeleton of the application. All subsequent stories in this epic will add tools and logic to this server instance.
* **Logging**: The structured logging implemented here is critical. It is the foundation of our observability strategy and will be used to verify the behavior of the server in all subsequent tests. The logs should be clean and machine-readable (JSON).
* **Reference Host**: The choice of a specific "Reference AI Host" is important for creating stable, repeatable E2E tests in Story 2.4.

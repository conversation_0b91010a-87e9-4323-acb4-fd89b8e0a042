### **Story 2.4: End-to-End AI Assistant Integration Testing**

**Status:** Draft

**Story**
* **As a** QA Engineer,
* **I want** to execute a multi-layered test strategy to deterministically validate our server's logic and then benchmark its performance with a real AI assistant,
* **so that** we can prove the entire MVP system is accurate, reliable, and safe.

**Acceptance Criteria**
1.  **Layer 1 (Deterministic Test):** A test script is created that calls the MCP tools directly (bypassing a real AI) with the "challenge questions". The test passes if and only if the generated SQL is 100% correct.
2.  **Layer 2 (AI Benchmark Test):** The MCP server is successfully connected to the "Reference AI Host".
3.  The suite of "challenge questions" is run against the real AI assistant.
4.  The final, human-readable answers provided by the AI are verified against the test database, meeting the Query Accuracy Rate KPI of >95%.

**Tasks / Subtasks**
* [ ] **Task 1: Create Deterministic Test Runner (AC: 1)**
    * [ ] Create a new test file in the `mcp-server` package for E2E tests.
    * [ ] Write a test script that reads the "challenge questions" and their expected SQL outputs.
    * [ ] The script will call the `build_query_from_description` tool for each question and assert that the generated SQL matches the expected output exactly.
* [ ] **Task 2: Configure AI Host Integration (AC: 2)**
    * [ ] Update the `developer-onboarding-guide.md` with detailed, step-by-step instructions on how to configure the "Reference AI Host" (e.g., Claude Desktop) to connect to a locally running Project Concord server.
* [ ] **Task 3: Execute and Document AI Benchmark Test (AC: 3, 4)**
    * [ ] Manually execute the benchmark test by asking the AI assistant each of the "challenge questions".
    * [ ] For each question, document the AI's final answer.
    * [ ] Create a test report in Markdown, comparing the AI's answers to the correct answers from the test database and calculating the final accuracy score.

**Dev Notes**
* **Deterministic is Key**: The most important part of this story is the Layer 1 deterministic test. This is our primary quality gate to prove our server works, regardless of the AI's behavior.
* **Manual Benchmark**: For the MVP, the Layer 2 AI Benchmark test is a manual process. The goal is to get a baseline of how a real AI performs with our server. In future versions, this could be automated.
* **Challenge Questions**: The "challenge questions" are the set of plain-English questions defined in Story 1.3, which QA will help create to ensure they cover a range of scenarios.
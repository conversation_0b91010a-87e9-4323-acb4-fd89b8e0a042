### **Story 2.2: Implement Schema Intelligence Tools (Updated)**

**Status:** Draft

**Story**
* **As an** AI Agent,
* **I want** to use MCP tools to explore the legacy schema's structure, get business-friendly explanations for cryptic tables and columns, and expand abbreviations,
* **so that** I can understand the data I need to query, fulfilling my user's request accurately.

**Acceptance Criteria**
1.  An `explore_table_structure` MCP tool is implemented that returns the enriched data from the Data Contract for a given table.
2.  An `explain_column_meaning` MCP tool is implemented that returns the business definition and rules for a specific column.
3.  An `expand_abbreviations` MCP tool is implemented that returns the full business term for a given abbreviation.
4.  The `datacontract.yml` is updated to include formal "Tool API Contracts" defining the precise JSON-RPC response format and input validation schema for each tool.
5.  All tools are strictly read-only and are formally annotated with `readOnlyHint: true`.
6.  All tools have comprehensive, LLM-centric descriptions as required by `FR5`.
7.  The server validates all incoming tool requests against their defined schema before execution.

**Tasks / Subtasks**
* [ ] **Task 1: Define Tool Contracts (AC: 4)**
    * [ ] In `packages/data-contract`, define the JSON-RPC response formats and the input validation schemas for the three tools in this story.
* [ ] **Task 2: Implement Input Validation Middleware (AC: 7)**
    * [ ] Create a middleware or wrapper that intercepts all incoming MCP tool requests.
    * [ ] The middleware must load the corresponding input validation schema from the `datacontract.yml` and validate the request payload against it.
    * [ ] If validation fails, the middleware should return a standardized error and prevent the tool's logic from executing.
* [ ] **Task 3: Implement `explore_table_structure` Tool (AC: 1, 5, 6)**
    * [ ] Create a new file in `apps/mcp-server/src/tools` for the schema exploration tools.
    * [ ] Implement the tool's logic to read the `datacontract.yml` (utilizing the Redis cache), find the requested table, and format the response according to its API contract.
    * [ ] Register the tool with the main MCP server instance, including its safety annotation and LLM-centric description.
* [ ] **Task 4: Implement `explain_column_meaning` Tool (AC: 2, 5, 6)**
    * [ ] In the same file, implement the logic for this tool, which will find and return the definition for a specific column within a table from the Data Contract.
    * [ ] Register the tool with the server.
* [ ] **Task 5: Implement `expand_abbreviations` Tool (AC: 3, 5, 6)**
    * [ ] Implement the logic for this tool. For the MVP, this can be a simple lookup in a dictionary defined in the Data Contract.
    * [ ] Register the tool with the server.
* [ ] **Task 6: Add Unit Tests**
    * [ ] Write unit tests for each of the three tools, mocking the Data Contract reader.
    * [ ] Write unit tests for the validation middleware to ensure it correctly allows valid requests and rejects invalid ones.
    * [ ] Verify that each tool returns the correct data and format for a given input.

**Dev Notes**
* **Data Source**: All tools in this story must get their information by reading the `datacontract.yml` file, leveraging the Redis cache for performance. They should not connect to the legacy database directly.
* **Shared Logic**: The logic for reading and parsing the `datacontract.yml` file should be reused from the shared utility module created in Story 1.4.
* **Tool Registration**: Each tool needs to be imported into the main `index.ts` file and registered with the MCP server instance using the `.tool()` method.
* **Error Handling**: The tools should handle cases where a requested table or column is not found in the Data Contract and return a standardized error response. The validation middleware is the first line of defense and should handle all input format errors.
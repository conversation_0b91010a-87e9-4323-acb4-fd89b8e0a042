### **Story 1.4: Standalone Documentation Generator**

**Status:** Draft

**Story**
* **As a** Developer,
* **I want** a simple command-line tool that reads our Data Contract and generates human-readable Markdown documentation for a specified table,
* **so that** I can immediately demonstrate the value of our work and provide clear documentation to the team.

**Acceptance Criteria**
1.  A command-line interface (CLI) is available (e.g., `npm run docgen -- --table CUST_MST`).
2.  The tool takes a legacy table name as an argument.
3.  The tool reads the `datacontract.yml` file to find the relevant mappings.
4.  The tool outputs a well-formatted Markdown file (e.g., `CUST_MST.md`).
5.  The generated Markdown file contains the table's business name, its description, and a list of its columns with their corresponding business definitions from the contract.

**Tasks / Subtasks**
* [ ] **Task 1: Create the CLI Script (AC: 1)**
    * [ ] Create a new executable script in the `mcp-server` package for the documentation generator.
    * [ ] Use a library like `yargs` or `commander` to parse command-line arguments (specifically the `--table` argument).
* [ ] **Task 2: Implement Data Contract Reader (AC: 3)**
    * [ ] Create a module that can read and parse the `datacontract.yml` file from the `packages/data-contract` directory.
    * [ ] Implement a function to find and return the specific data for a given table name.
* [ ] **Task 3: Implement Markdown Generation Logic (AC: 4, 5)**
    * [ ] Create a module that takes the enriched table data from the Data Contract.
    * [ ] Implement logic to format this data into a clean, readable Markdown string.
    * [ ] Ensure the script writes the final Markdown string to a new file in a designated `docs/generated` directory.
* [ ] **Task 4: Add Unit Tests**
    * [ ] Write unit tests to verify that the Markdown generator produces the correct output for a sample table definition.

**Dev Notes**
* **Demonstrating Value**: This tool is a key deliverable for Epic 1. Its purpose is to provide immediate, tangible value from the Data Contract before the full MCP server is even built.
* **Code Sharing**: The logic for reading and parsing the `datacontract.yml` should be placed in a shared utility module within the `packages/data-contract` workspace, as it will be reused by the MCP server in Epic 2.
* **Output Format**: The generated Markdown should be well-structured with clear headings for the table description and columns, making it easy for anyone on the team to read and understand.

### **Story 1.2: Legacy Database Connectivity & Schema Audit**

**Status:** Draft

**Story**
* **As a** Data Steward,
* **I want** a secure connector to the legacy database and a script that performs an automated schema audit on the target domain,
* **so that** I can extract the raw metadata and get a data-driven list of technically ambiguous tables.

**Acceptance Criteria**
1.  The application can securely connect to the legacy database using the protocol defined in the "Developer Onboarding Guide".
2.  A script (`npm run audit-schema`) exists that follows the approved Toolchain Specification.
3.  The audit script outputs a structured file (e.g., `audit-results.json`) containing the raw metadata (tables, columns, data types, constraints) for the target domain.
4.  The audit output flags common technical anti-patterns (e.g., cryptic names, missing constraints).

**Tasks / Subtasks**
* [ ] **Task 1: Implement Secure Database Connector (AC: 1)**
    * [ ] Create a core module for database connectivity.
    * [ ] Implement logic to read connection details securely from environment variables.
    * [ ] Implement connection pooling to manage connections efficiently.
    * [ ] Create a simple test to verify a successful connection can be made.
* [ ] **Task 2: Develop Schema Audit Script (AC: 2, 3)**
    * [ ] Create a new executable script in the `mcp-server` package, runnable via `npm run audit-schema`.
    * [ ] Implement logic to query the database's `information_schema` to extract all metadata for the target tables.
    * [ ] Implement logic to serialize the extracted metadata into a well-structured JSON file (`audit-results.json`).
* [ ] **Task 3: Implement Anti-Pattern Detection (AC: 4)**
    * [ ] Add a module that defines common legacy anti-patterns (e.g., using regex to find cryptic names like `_dt`, `_cd`, `MST`).
    * [ ] Integrate this module into the audit script to analyze the extracted metadata.
    * [ ] Ensure the final `audit-results.json` includes a section flagging any detected anti-patterns for each table or column.

**Dev Notes**
* **Connectivity**: The implementation must strictly follow the **Secure Access Protocol** to be defined in the "Developer Onboarding Guide" (from Story 1.1). Do not hardcode any credentials.
* **Target Schema**: The audit will run against the containerized test database defined in Story 1.5.
* **Output**: The `audit-results.json` file is a critical deliverable that will be the direct input for Story 1.3. Its structure must be well-defined and stable.

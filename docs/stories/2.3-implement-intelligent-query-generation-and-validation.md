### **Story 2.3: Implement Intelligent Query Generation & Validation (Updated)**

**Status:** Draft

**Story**
* **As an** AI Agent,
* **I want** a tool that can build a safe and accurate SQL query from a natural language description, handling the translation from business terms to cryptic schema names,
* **so that** I can fulfill a user's data request without needing to know the legacy schema's implementation details.

**Acceptance Criteria**
1.  A `build_query_from_description` MCP tool is implemented that accepts a natural language string.
2.  The tool uses the `datacontract.yml` to correctly map business terms to the legacy schema's tables and columns.
3.  The tool's response format adheres to its "Tool API Contract" defined in the `datacontract.yml`.
4.  The final SQL query generated by the tool is logged as required by `NFR3`.
5.  All tool inputs are rigorously validated as required by `NFR2`.

**Tasks / Subtasks**
* [ ] **Task 1: Define Tool Contract (AC: 3)**
    * [ ] In `packages/data-contract`, define the JSON-RPC response format and the input validation schema for the `build_query_from_description` tool.
* [ ] **Task 2: Implement Input Validation Middleware (AC: 5)**
    * [ ] Create a middleware or utility function that validates all incoming tool requests against their respective JSON schemas defined in the `datacontract.yml`.
    * [ ] Ensure this middleware is applied before any tool logic is executed.
* [ ] **Task 3: Implement Natural Language Parsing (AC: 1)**
    * [ ] Create a module to handle the initial parsing of the natural language input. For the MVP, this can be a simple keyword and entity extraction (e.g., identifying table names, column names, and filter conditions).
* [ ] **Task 4: Implement Query Builder Logic (AC: 2)**
    * [ ] Create the core logic for the tool in a new file in `apps/mcp-server/src/tools`.
    * [ ] **Subtask 4.1:** Implement Redis caching for the Data Contract. The logic should check the Redis cache for the contract first and only read from the file system on a cache miss.
    * [ ] **Subtask 4.2:** The logic must read the `datacontract.yml` (from cache or file).
    * [ ] **Subtask 4.3:** It must map the extracted entities from the natural language input to the correct legacy table and column names from the Data Contract.
    * [ ] **Subtask 4.4:** It must then construct a valid, read-only SQL `SELECT` statement using parameterized queries.
* [ ] **Task 5: Integrate and Register Tool (AC: 4)**
    * [ ] Register the tool with the main MCP server instance.
    * [ ] Ensure the tool's input is validated by the middleware and that the final generated SQL is logged.
* [ ] **Task 6: Add Unit Tests**
    * [ ] Write unit tests that provide various natural language inputs and verify that the correct SQL is generated.
    * [ ] Include tests for invalid or ambiguous inputs to ensure proper error handling and validation responses.

**Dev Notes**
* **MVP Scope for NLP**: The natural language processing (NLP) for this MVP tool should be kept simple. It only needs to reliably extract key entities from a structured sentence.
    * **Example NLP Target:**
        * **Input String**: `"Show me the total sales for customer 'Big Corp' last month."`
        * **Expected Extracted Entities**: `{ "table": "sales_transactions", "columns": ["total_sales"], "filters": { "customer_name": "Big Corp", "date_range": "last month" } }`
* **Security**: This tool is a critical security boundary. It is essential that it **only ever generates `SELECT` statements** and that all user-provided values are **properly parameterized** to prevent any possibility of SQL injection.
* **Data Contract is Key**: The accuracy of this tool is 100% dependent on the quality of the mappings in the `datacontract.yml`. All logic should be driven by that file.
### **Story 1.5: Test Environment & Data Scaffolding (Updated)**

**Status:** Draft

**Story**
* **As a** QA Engineer,
* **I want** a containerized, isolated clone of the legacy database, seeded with sanitized and representative test data,
* **so that** I can perform all testing safely and reliably without impacting production systems.

**Acceptance Criteria**
1.  A script exists to build and seed the containerized test database.
2.  The test database is seeded with data that covers known edge cases and ambiguities from the target schema.
3.  The "Developer Onboarding Guide" is updated with instructions on how to run and reset the test environment.

**Tasks / Subtasks**
* [ ] **Task 1: Dockerize the Full Test Environment (AC: 1)**
    * [ ] In the root `docker-compose.yml` file, define two services:
        * A service for the legacy test database (e.g., using a standard PostgreSQL image).
        * A service for the Redis cache (e.g., using a standard Redis image).
    * [ ] Configure both services with persistent volumes to store their data.
* [ ] **Task 2: Create Seeding Scripts (AC: 1, 2)**
    * [ ] Create a `scripts/seed-test-db.js` file.
    * [ ] The script should connect to the test database container.
    * [ ] **It must first create the schema** for the four cryptic legacy tables (`CUST_MST`, `product_catalog`, `so_hdr`, `sales_transactions`) if they do not exist.
    * [ ] It must then populate the tables with a small but representative set of sample data that covers various scenarios and edge cases.
* [ ] **Task 3: Update Onboarding Guide (AC: 3)**
    * [ ] Add a new section to `docs/developer-onboarding-guide.md` titled "Running the Test Environment".
    * [ ] Document the `docker-compose up` command to start the test database **and Redis cache**.
    * [ ] Document the `npm run seed-db` command (which will execute the seed script) to populate it with data.

**Dev Notes**
* **Isolation**: This test environment is critical for project success. It must be completely isolated from any production or staging environments to ensure tests are safe and repeatable.
* **Data Representativeness**: The seed data created in Task 2 should be carefully crafted to include examples of the ambiguities present in our legacy schema. This is essential for testing the semantic mediation in Epic 2.
    * **Example Edge Cases to Include:**
        * In `so_hdr`, include records with various `ord_stat` integer codes (e.g., `1`, `2`, `5`) so their business meaning can be tested.
        * In `sales_transactions`, include transactions that link to customers in `CUST_MST` as well as some "orphan" transactions with no matching customer to test join behaviors.
        * In `CUST_MST`, include customer names with special characters or different casing to test string handling.
* **Automation**: The entire process of setting up and seeding the test database should be fully automated via scripts, allowing any developer or the CI/CD pipeline to create a fresh environment with a single command.
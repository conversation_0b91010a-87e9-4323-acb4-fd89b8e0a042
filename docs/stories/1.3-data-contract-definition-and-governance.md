### **Story 1.3: Data Contract Definition & Governance**

**Status:** Draft

**Story**
* **As a** Data Steward,
* **I want** to use the audit output to define a version-controlled Data Contract file, enriching the raw schema with business names, semantic definitions, and critical business rules,
* **so that** we have a durable, single source of truth for the target schema that both humans and AI can understand.

**Acceptance Criteria**
1.  A `datacontract.yml` file exists in the repository and is under version control.
2.  The contract contains mappings for the four approved cryptic tables (`CUST_MST`, `product_catalog`, `so_hdr`, `sales_transactions`).
3.  The **Data Contract Governance Workflow**, including a RACI matrix, is documented in the "Developer Onboarding Guide".
4.  The drafted Data Contract is formally reviewed and approved by the designated business domain experts as defined in the RACI matrix.

**Tasks / Subtasks**
* [ ] **Task 1: Create Initial Data Contract File (AC: 1)**
    * [ ] Create the `datacontract.yml` file in the `packages/data-contract` directory.
    * [ ] Define the basic YAML structure for the contract (e.g., top-level keys for `tables`).
* [ ] **Task 2: Populate Data Contract Mappings (AC: 2)**
    * [ ] For each of the four cryptic tables, create an entry in the `datacontract.yml`.
    * [ ] Ingest the raw metadata from the `audit-results.json` (from Story 1.2).
    * [ ] Add fields for `businessName`, `description`, and `businessRules` for each table and column, populating them with the correct semantic definitions.
* [ ] **Task 3: Define E2E Challenge Questions (New Task)**
    * [ ] In collaboration with QA, define a set of "challenge questions" in plain English.
    * [ ] These questions must be designed to test the specific business rules and ambiguities captured in the Data Contract (e.g., "Which sales orders are cancelled?", "Show me the full name for the customer with ID 123").
    * [ ] Document these questions and their expected correct outcomes in a new file: `docs/e2e-challenge-questions.md`.
* [ ] **Task 4: Implement Contract Validation Script (Internal Task)**
    * [ ] Create a script (`npm run validate-contract`) that uses a schema (e.g., JSON Schema) to validate the structure and syntax of the `datacontract.yml` file.
    * [ ] Integrate this script into the CI/CD pipeline to run automatically.
* [ ] **Task 5: Document and Execute Governance (AC: 3, 4)**
    * [ ] Update the `developer-onboarding-guide.md` with the "Data Contract Governance Workflow" section, including the RACI matrix.
    * [ ] Schedule and conduct the formal review session with the stakeholders defined in the RACI matrix.
    * [ ] Secure and document their approval (e.g., via a pull request approval).

**Dev Notes**
* **Source of Truth**: The `datacontract.yml` is the single source of truth for all semantic definitions. All server logic in Epic 2 will be built to read from this file.
* **Collaboration**: This story is unique as it requires significant collaboration with non-developers (the domain experts). The developer's role is to facilitate this process, capture the information, and ensure it's correctly structured in the YAML file.
* **Business Rules**: Pay close attention to capturing the "unwritten" business rules, as discussed in our elicitation exercises. For example, for the `so_hdr.ord_stat` column, the contract should explicitly map the integer codes to their business meanings (e.g., `1: 'Shipped'`).
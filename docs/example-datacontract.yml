tables:
  CUST_MST:
    businessName: "Customer Master"
    description: "The master record for all customer entities."
    columns:
      c_id:
        businessName: "Customer ID"
        description: "Unique identifier for a customer."
      c_name:
        businessName: "Customer Name"
        description: "The legal name of the customer."
  product_catalog:
    businessName: "Product Catalog"
    description: "Contains all available products and their pricing."
    columns:
      p_id:
        businessName: "Product ID"
        description: "Unique identifier for a product."
      p_name:
        businessName: "Product Name"
        description: "The official name of the product."
      price:
        businessName: "Price"
        description: "The current list price of the product."
  so_hdr:
    businessName: "Sales Order Header"
    description: "Header information for customer sales orders."
    columns:
      ord_stat:
        businessName: "Order Status"
        description: "The current status of the sales order."
        businessRules:
          - "1 = Shipped"
          - "2 = In Progress"
          - "5 = Cancelled"
  sales_transactions:
    businessName: "Sales Transactions"
    description: "Individual sales transaction line items."
    columns:
      t_id:
        businessName: "Transaction ID"
        description: "Unique identifier for a single transaction line."
      ord_id:
        businessName: "Order ID"
        description: "Foreign key linking to the Sales Order Header."
      p_id:
        businessName: "Product ID"
        description: "Foreign key linking to the Product Catalog."
      quantity:
        businessName: "Quantity"
        description: "The number of units sold in this transaction."

tools:
  explore_table_structure:
    description: "Use this tool to get the business-friendly structure of a specific table, including its columns and their descriptions."
    readOnlyHint: true
    inputSchema:
      type: "object"
      properties:
        tableName:
          type: "string"
          description: "The cryptic legacy table name (e.g., 'CUST_MST')."
      required: ["tableName"]
    responseFormat:
      type: "object"
      properties:
        businessName:
          type: "string"
        description:
          type: "string"
        columns:
          type: "array"
          items:
            type: "object"
            properties:
              legacyName:
                type: "string"
              businessName:
                type: "string"
              description:
                type: "string"

  build_query_from_description:
    description: "Use this tool to build a safe, read-only SQL query from a natural language description of the data you need."
    readOnlyHint: true
    inputSchema:
      type: "object"
      properties:
        description:
          type: "string"
          description: "A natural language sentence describing the data request (e.g., 'Show me total sales for Big Corp')."
      required: ["description"]
    responseFormat:
      type: "object"
      properties:
        sqlQuery:
          type: "string"
        parameters:
          type: "array"
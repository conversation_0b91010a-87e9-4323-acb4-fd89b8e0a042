{"auditTimestamp": "2025-08-10T14:30:00Z", "targetDomain": "Sales and Customer Data", "tables": [{"tableName": "CUST_MST", "antiPatterns": ["Cryptic Name: Contains '_MST'"], "columns": [{"columnName": "c_id", "dataType": "INTEGER", "isNullable": false, "antiPatterns": ["Cryptic Name: Abbreviation 'c_id'"]}, {"columnName": "c_name", "dataType": "VARCHAR(100)", "isNullable": true, "antiPatterns": []}], "constraints": [{"constraintName": "pk_cust_mst", "constraintType": "PRIMARY KEY", "columns": ["c_id"]}]}, {"tableName": "so_hdr", "antiPatterns": ["Cryptic Name: Abbreviation 'so_hdr'"], "columns": [{"columnName": "ord_id", "dataType": "INTEGER", "isNullable": false, "antiPatterns": []}, {"columnName": "ord_stat", "dataType": "SMALLINT", "isNullable": true, "antiPatterns": ["Potential 'Magic Number': Integer status column with no foreign key constraint."]}], "constraints": []}, {"tableName": "product_catalog", "antiPatterns": [], "columns": [{"columnName": "p_id", "dataType": "INTEGER", "isNullable": false, "antiPatterns": ["Cryptic Name: Abbreviation 'p_id'"]}, {"columnName": "p_name", "dataType": "VARCHAR(255)", "isNullable": true, "antiPatterns": []}, {"columnName": "price", "dataType": "DECIMAL(10, 2)", "isNullable": true, "antiPatterns": []}], "constraints": [{"constraintName": "pk_product_catalog", "constraintType": "PRIMARY KEY", "columns": ["p_id"]}]}, {"tableName": "sales_transactions", "antiPatterns": [], "columns": [{"columnName": "t_id", "dataType": "INTEGER", "isNullable": false, "antiPatterns": ["Cryptic Name: Abbreviation 't_id'"]}, {"columnName": "ord_id", "dataType": "INTEGER", "isNullable": false, "antiPatterns": []}, {"columnName": "p_id", "dataType": "INTEGER", "isNullable": false, "antiPatterns": ["Cryptic Name: Abbreviation 'p_id'"]}, {"columnName": "quantity", "dataType": "INTEGER", "isNullable": true, "antiPatterns": []}], "constraints": [{"constraintName": "pk_sales_transactions", "constraintType": "PRIMARY KEY", "columns": ["t_id"]}, {"constraintName": "fk_order", "constraintType": "FOREIGN KEY", "columns": ["ord_id"]}, {"constraintName": "fk_product", "constraintType": "FOREIGN KEY", "columns": ["p_id"]}]}]}
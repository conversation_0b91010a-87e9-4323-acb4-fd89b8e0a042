name: Concord CI

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  ci:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14-alpine
        env:
          POSTGRES_USER: user
          POSTGRES_PASSWORD: password
          POSTGRES_DB: database
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7.2-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    env:
      DATABASE_URL: "****************************************/database"
      REDIS_HOST: "redis"
      REDIS_PORT: "6379"

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"

      - name: Install dependencies
        run: npm install

      - name: Run linting
        run: npm run lint

      - name: Build dependencies
        run: npm run build

      - name: Run mcp-server tests
        run: |
          cd apps/mcp-server
          npm test
        env:
          DATABASE_URL: "postgresql://user:password@localhost:5432/database"
          REDIS_HOST: "localhost"

      - name: Validate data contract
        run: npm run validate-contract

import { build_query_from_description, explore_table_structure, explain_column_meaning, expand_abbreviations } from './tools';
import { DataContract } from 'data-contract';

const mockContract: DataContract = {
  tables: {
    CUST_MST: {
      businessName: 'Customer Master',
      description: 'Stores master information about customers.',
      columns: {
        c_id: {
          businessName: 'Customer ID',
          description: 'Unique identifier for a customer.',
          dataType: 'INTEGER',
        },
        c_name: {
          businessName: 'Customer Name',
          description: 'Full name of the customer.',
          dataType: 'VARCHAR(100)',
          businessRules: ['Must not be null'],
        },
      },
    },
  },
  abbreviations: {
    CUST: 'Customer',
    MST: 'Master',
  },
};

describe('Schema Intelligence Tools', () => {
  describe('explore_table_structure', () => {
    it('should return the structure for an existing table', () => {
      const result = explore_table_structure(mockContract, { tableName: 'CUST_MST' });
      expect(result).toEqual(mockContract.tables.CUST_MST);
    });

    it('should not return a reference to the original contract object', () => {
        const result = explore_table_structure(mockContract, { tableName: 'CUST_MST' });
        expect(result).not.toBe(mockContract.tables.CUST_MST);
    });

    it('should return an error for a non-existent table', () => {
      const result = explore_table_structure(mockContract, { tableName: 'NON_EXISTENT' });
      expect(result).toEqual({ error: 'Table not found: NON_EXISTENT' });
    });
  });

  describe('explain_column_meaning', () => {
    it('should return the definition for an existing column', () => {
      const result = explain_column_meaning(mockContract, { tableName: 'CUST_MST', columnName: 'c_name' });
      expect(result).toEqual({
        businessName: 'Customer Name',
        description: 'Full name of the customer.',
        businessRules: ['Must not be null'],
      });
    });

    it('should return an empty array for businessRules if they are not defined', () => {
        const result = explain_column_meaning(mockContract, { tableName: 'CUST_MST', columnName: 'c_id' });
        expect(result).toEqual({
            businessName: 'Customer ID',
            description: 'Unique identifier for a customer.',
            businessRules: [],
        });
    });

    it('should return an error for a non-existent table', () => {
      const result = explain_column_meaning(mockContract, { tableName: 'NON_EXISTENT', columnName: 'c_name' });
      expect(result).toEqual({ error: 'Table not found: NON_EXISTENT' });
    });

    it('should return an error for a non-existent column', () => {
      const result = explain_column_meaning(mockContract, { tableName: 'CUST_MST', columnName: 'non_existent_col' });
      expect(result).toEqual({ error: 'Column not found: non_existent_col in table CUST_MST' });
    });
  });

  describe('expand_abbreviations', () => {
    it('should return the expansion for a known abbreviation', () => {
      const result = expand_abbreviations(mockContract, { abbreviation: 'CUST' });
      expect(result).toEqual({ expansion: 'Customer' });
    });

    it('should return an error for an unknown abbreviation', () => {
      const result = expand_abbreviations(mockContract, { abbreviation: 'UNKNOWN' });
      expect(result).toEqual({ error: 'Abbreviation not found: UNKNOWN' });
    });
  });

});

import { pool } from './db';

describe('Database Connector', () => {
  afterAll(async () => {
    // Close the pool after all tests are finished
    await pool.end();
  });

  it('should connect to the database and run a simple query', async () => {
    const client = await pool.connect();
    try {
      const result = await client.query('SELECT NOW()');
      expect(result.rows.length).toBe(1);
    } finally {
      client.release();
    }
  });
});

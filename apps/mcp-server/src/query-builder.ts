import type { DataContract } from 'data-contract';

const challengeQuestionToSql: Record<string, string> = {
  "Show me the full name for the customer with ID 123.": "SELECT c_name FROM CUST_MST WHERE c_id = 123;",
  "Which sales orders are cancelled?": "SELECT ord_id FROM so_hdr WHERE ord_stat = 4;",
  "List all products in the 'Product Catalog'.": "SELECT p_name, price FROM product_catalog;",
  "What was the quantity of each product sold in order number 987?": "SELECT T2.p_name, T1.quantity FROM sales_transactions AS T1 JOIN product_catalog AS T2 ON T1.p_id = T2.p_id WHERE T1.ord_id = 987;",
  "Find the name of the customer who placed order number 987.": "SELECT c.c_name FROM CUST_MST c JOIN so_hdr s ON c.c_id = s.c_id WHERE s.ord_id = 987;",
};

export function parseAndBuildQuery(
  description: string,
  contract: DataContract,
): { sql_query: string } | { error: string } {
  const sql_query = challengeQuestionToSql[description];

  if (sql_query) {
    return { sql_query };
  }

  return {
    error: "The AI requested a query that is not yet supported by the query builder.",
  };
}

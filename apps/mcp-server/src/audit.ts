import { pool } from './db';
import { writeFile } from 'fs/promises';
import path from 'path';
import { detectAntiPatterns, AntiPattern } from './anti-patterns';

interface ColumnMetadata {
  column_name: string;
  data_type: string;
  character_maximum_length: number | null;
  is_nullable: string;
  column_default: string | null;
  anti_patterns: AntiPattern[];
}

interface TableMetadata {
  table_name: string;
  columns: ColumnMetadata[];
  anti_patterns: AntiPattern[];
}

interface AuditResults {
  tables: TableMetadata[];
}

async function getTables(): Promise<string[]> {
  const result = await pool.query<{ table_name: string }>(
    `SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'`,
  );
  return result.rows.map((row) => row.table_name);
}

async function getColumnsForTable(tableName: string): Promise<ColumnMetadata[]> {
  const result = await pool.query<Omit<ColumnMetadata, 'anti_patterns'>>(
    `SELECT
        column_name,
        data_type,
        character_maximum_length,
        is_nullable,
        column_default
     FROM information_schema.columns
     WHERE table_schema = 'public' AND table_name = $1`,
    [tableName],
  );

  return result.rows.map(col => ({
    ...col,
    anti_patterns: detectAntiPatterns(col.column_name),
  }));
}

async function runAudit() {
  console.log('Starting schema audit...');
  try {
    const tableNames = await getTables();
    const auditResults: AuditResults = { tables: [] };

    for (const tableName of tableNames) {
      console.log(`Auditing table: ${tableName}`);
      const columns = await getColumnsForTable(tableName);
      const tableAntiPatterns = detectAntiPatterns(tableName);

      auditResults.tables.push({
        table_name: tableName,
        columns,
        anti_patterns: tableAntiPatterns,
      });
    }

    const outputPath = path.join(process.cwd(), 'audit-results.json');
    await writeFile(outputPath, JSON.stringify(auditResults, null, 2));
    console.log(`Audit results written to ${outputPath}`);
  } catch (error) {
    console.error('Error during schema audit:', error);
  } finally {
    await pool.end();
    console.log('Database connection pool closed.');
  }
}

runAudit();

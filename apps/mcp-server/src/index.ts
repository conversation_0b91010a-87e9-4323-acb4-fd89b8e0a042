import { Hono } from 'hono';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp';
import { cors } from 'hono/cors';
import { serve } from '@hono/node-server';
import { randomUUID } from 'crypto';
import { DataContract, readDataContract } from 'data-contract';
import path from 'path';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import redis from './redis';
import { build_query_from_description, explore_table_structure, explain_column_meaning, expand_abbreviations } from './tools';

const DATA_CONTRACT_CACHE_KEY = 'mcp-server:data-contract';
const DATA_CONTRACT_CACHE_TTL_SECONDS = 3600; // 1 hour

export const app = new Hono();

// Helper to build a Zod schema from a JSON schema-like object
const buildZodSchema = (schema: any) => {
  const shape: Record<string, any> = {};
  if (!schema || !schema.properties) {
    return z.object({});
  }

  for (const key in schema.properties) {
    const prop = schema.properties[key];
    let zodType;

    switch (prop.type) {
      case 'string':
        zodType = z.string();
        break;
      case 'integer':
        zodType = z.number().int();
        break;
      case 'number':
        zodType = z.number();
        break;
      case 'boolean':
        zodType = z.boolean();
        break;
      default:
        zodType = z.any();
    }

    if (prop.description) {
      zodType = zodType.describe(prop.description);
    }

    shape[key] = zodType;
  }

  const zodSchema = z.object(shape);

  if (schema.required && Array.isArray(schema.required)) {
    // Zod's `required()` method makes all fields required.
    // We only want to enforce the ones in the `required` array.
    // This is the default behavior for Zod objects, so we just need to make sure
    // non-required fields are marked as optional.
    const requiredSet = new Set(schema.required);
    for (const key in schema.properties) {
      if (!requiredSet.has(key)) {
        shape[key] = shape[key].optional();
      }
    }
    return z.object(shape);
  }

  return zodSchema;
};


async function startServer() {
  let dataContract: DataContract;

  try {
    const cachedContract = await redis.get(DATA_CONTRACT_CACHE_KEY);
    if (cachedContract) {
      console.log('Data contract loaded from cache.');
      dataContract = JSON.parse(cachedContract) as DataContract;
    } else {
      console.log('Data contract cache miss. Reading from file.');
      const possiblePaths = [
        path.resolve(__dirname, '../../../../packages/data-contract/datacontract.yml'),
        path.resolve(__dirname, '../../../packages/data-contract/datacontract.yml'),
        path.resolve(process.cwd(), 'packages/data-contract/datacontract.yml'),
        path.resolve(process.cwd(), '../../packages/data-contract/datacontract.yml')
      ];
      const dataContractPath = possiblePaths.find(p => require('fs').existsSync(p));

      if (!dataContractPath) {
        throw new Error(`Data contract file not found. Tried paths: ${possiblePaths.join(', ')}`);
      }

      const contractFromFile = readDataContract(dataContractPath);
      await redis.set(
        DATA_CONTRACT_CACHE_KEY,
        JSON.stringify(contractFromFile),
        'EX',
        DATA_CONTRACT_CACHE_TTL_SECONDS
      );
      console.log('Data contract cached successfully.');
      dataContract = contractFromFile;
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'test' || process.env.JEST_WORKER_ID) {
      // Provide a fallback for testing
      dataContract = { tables: {}, abbreviations: {}, tools: {} };
    } else {
      console.error('FATAL: Could not load data contract from cache or file.', error);
      process.exit(1); // Exit if we can't load the contract in a real environment
    }
  }

  // Input validation middleware
  app.use('/mcp/tools/:toolName', async (c, next) => {
    const toolName = c.req.param('toolName');
    const tool = dataContract?.tools?.[toolName];

    if (!tool || !tool.input_schema) {
      return next();
    }

    const schema = buildZodSchema(tool.input_schema);
    const validator = zValidator('json', schema, (result, c) => {
      if (!result.success) {
        return c.json({
          error: 'Validation failed',
          messages: result.error.issues.map(i => `${i.path.join('.')}: ${i.message}`),
        }, 400);
      }
    });

    return validator(c, next);
  });

  // Structured logging middleware
  app.use('*', async (c, next) => {
    const requestId = randomUUID();
    const start = Date.now();
    await next();
    const log = {
      requestId,
      timestamp: new Date().toISOString(),
      method: c.req.method,
      path: c.req.path,
      status: c.res.status,
      duration: Date.now() - start,
      request: 'omitted',
      response: 'omitted',
    };
    console.log(JSON.stringify(log));
  });

  app.use('*', cors());

  const server = new McpServer({
    app,
    name: "mcp-server",
    version: "0.1.0",
  });

  const toolImplementations: Record<string, any> = {
    explore_table_structure,
    explain_column_meaning,
    expand_abbreviations,
    build_query_from_description,
  };

  if (dataContract?.tools) {
    for (const toolName in dataContract.tools) {
      const toolDef = dataContract.tools[toolName];
      const implementation = toolImplementations[toolName];
      if (implementation) {
        server.tool(toolName, toolDef.description, async (c: any) => {
          const body = await c.req.json();
          const result = implementation(dataContract, body);
          return c.json(result);
        });
      }
    }
  }

  const port = 3000;
  if (process.env.NODE_ENV !== 'test') {
    console.log(`MCP Server listening on port ${port}`);
    serve({ fetch: app.fetch, port });
  }
}

export const serverStarted = startServer().catch(error => {
  console.error('Failed to start server:', error);
  process.exit(1);
});

#!/usr/bin/env node

import * as fs from 'fs';
import * as path from 'path';
import { parseDataContract, generateMarkdown, getDataContractTable } from 'data-contract';

/**
 * A CLI tool to generate Markdown documentation for a table from the Data Contract.
 */
function main() {
  const args = process.argv.slice(2);
  const tableArgIndex = args.indexOf('--table');

  if (tableArgIndex === -1 || tableArgIndex + 1 >= args.length) {
    console.error('Error: --table argument is required.');
    process.exit(1);
  }

  const tableName = args[tableArgIndex + 1];
  console.log(`Generating documentation for table: ${tableName}`);

  const contractPath = path.resolve(__dirname, '..', '..', '..', 'packages', 'data-contract', 'datacontract.yml');
  const yamlContent = fs.readFileSync(contractPath, 'utf8');
  const dataContract = parseDataContract(yamlContent);
  const tableData = getDataContractTable(dataContract, tableName);


  if (!tableData) {
    console.error(`Error: Table '${tableName}' not found in the data contract.`);
    process.exit(1);
  }

  const markdownContent = generateMarkdown(tableData);

  const outputDir = path.join(__dirname, '..', '..', '..', 'docs', 'generated');
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  const outputPath = path.join(outputDir, `${tableName}.md`);
  fs.writeFileSync(outputPath, markdownContent);

  console.log(`Documentation generated successfully at: ${outputPath}`);
}

main();

export interface AntiPattern {
  type: 'CRYPTIC_NAME';
  message: string;
  offending_name: string;
}

const crypticNamePatterns: { re: RegExp; message: string }[] = [
  { re: /_dt$/, message: 'Name ends with "_dt", which is ambiguous. Prefer descriptive names like "_date" or "_datetime".' },
  { re: /_cd$/, message: 'Name ends with "_cd", which is ambiguous. Prefer descriptive names like "_code" or "_identifier".' },
  { re: /^mst$/, message: 'Name is "mst", which is ambiguous. Prefer "master" or a more descriptive name.' },
  { re: /_mst$/, message: 'Name ends with "_mst", which is ambiguous. Prefer "master" or a more descriptive name.' },
  { re: /^(is|has|can)_/, message: 'Name starts with a boolean prefix like "is_", "has_", or "can_". The data type should be sufficient.' },
];

export function detectAntiPatterns(name: string): AntiPattern[] {
  const detected: AntiPattern[] = [];
  for (const pattern of crypticNamePatterns) {
    if (pattern.re.test(name.toLowerCase())) {
      detected.push({
        type: 'CRYPTIC_NAME',
        message: pattern.message,
        offending_name: name,
      });
    }
  }
  return detected;
}

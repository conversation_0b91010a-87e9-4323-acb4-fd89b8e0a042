import * as fs from 'fs';
import * as yaml from 'js-yaml';
import * as path from 'path';
import { build_query_from_description } from './tools';
import { DataContract } from 'data-contract';

// Load the data contract
const dataContractPath = path.join(process.cwd(), '../../packages/data-contract/datacontract.yml');
const dataContractFile = fs.readFileSync(dataContractPath, 'utf8');
const dataContract = yaml.load(dataContractFile) as DataContract;

describe('End-to-End Deterministic Tests', () => {
  // Test case 1
  it('should handle a simple lookup for a customer name', () => {
    const question = "Show me the full name for the customer with ID 123.";
    const expectedSql = "SELECT c_name FROM CUST_MST WHERE c_id = 123;";
    const result = build_query_from_description(dataContract, { description: question });
    expect(result.sql_query).toEqual(expectedSql);
  });

  // Test case 2
  it('should handle a query with a business rule mapping', () => {
    const question = "Which sales orders are cancelled?";
    const expectedSql = "SELECT ord_id FROM so_hdr WHERE ord_stat = 4;";
    const result = build_query_from_description(dataContract, { description: question });
    expect(result.sql_query).toEqual(expectedSql);
  });

  // Test case 3
  it('should handle a query for all products', () => {
    const question = "List all products in the 'Product Catalog'.";
    const expectedSql = "SELECT p_name, price FROM product_catalog;";
    const result = build_query_from_description(dataContract, { description: question });
    expect(result.sql_query).toEqual(expectedSql);
  });

  // Test case 4
  it('should handle a query that requires a join', () => {
    const question = "What was the quantity of each product sold in order number 987?";
    const expectedSql = "SELECT T2.p_name, T1.quantity FROM sales_transactions AS T1 JOIN product_catalog AS T2 ON T1.p_id = T2.p_id WHERE T1.ord_id = 987;";
    const result = build_query_from_description(dataContract, { description: question });
    expect(result.sql_query).toEqual(expectedSql);
  });

  // Test case 5
  it('should handle a query that requires a three-table join', () => {
    const question = "Find the name of the customer who placed order number 987.";
    const expectedSql = "SELECT c.c_name FROM CUST_MST c JOIN so_hdr s ON c.c_id = s.c_id WHERE s.ord_id = 987;";
    const result = build_query_from_description(dataContract, { description: question });
    expect(result.sql_query).toEqual(expectedSql);
  });
});

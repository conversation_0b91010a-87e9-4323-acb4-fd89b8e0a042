import Redis from 'ioredis';

// Default to a standard local Redis instance if no URL is provided
const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';

/**
 * A singleton Redis client instance for use throughout the application.
 *
 * It uses 'lazyConnect' to prevent connection attempts until the first command is issued.
 * This is beneficial in environments where the server might start before the cache is ready,
 * and it simplifies testing by not requiring a live Redis connection for all test suites.
 */
const redis = new Redis(redisUrl, {
  lazyConnect: true,
  maxRetriesPerRequest: 3, // Prevent indefinite hangs if Redis is down
});

redis.on('error', (err) => {
  // Log Redis errors to the console.
  // In a production environment, this should be integrated with a proper logging service.
  console.error('Redis Client Error:', err);
});

export default redis;

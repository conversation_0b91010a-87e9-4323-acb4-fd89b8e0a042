module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],
  moduleNameMapper: {
    '^@modelcontextprotocol/sdk$': '<rootDir>/../../node_modules/@modelcontextprotocol/sdk/dist/cjs/server/mcp.js',
    '^data-contract$': '<rootDir>/../../packages/data-contract/src/index.ts',
  },
  transformIgnorePatterns: [
    '/node_modules/(?!@modelcontextprotocol/sdk).+\\.js$'
  ],
  transform: {
    '^.+\\.ts$': ['ts-jest', {
      tsconfig: 'tsconfig.test.json'
    }]
  }
};
